<!DOCTYPE html>
<html
  data-van-env="pre"
  data-van-idc="cn"
  data-van-project="self-introduction"
  data-van-sec="stable-1"
  data-van-task-id="296610"
  data-van-trace-id="950af6754a5611efbd4900163e.1830.17218921199942052"
  lang="en"
>
  <head>
    <script
      crossorigin="anonymous"
      src="https://static.huolala.cn/npm/vconsole@3.15.1/dist/vconsole.min.js"
    ></script>
    <script src="./dist/umd/index.js"></script>
    <script src="./dist/umd/index.js"></script>
    <!-- <script src="./swim.js"></script> -->
    <!-- <script src="https://tools-container-v.huolala.cn/dist/umd/index.js"></script> -->

    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1.0" /> -->
    <meta
      name="viewport"
      content="initial-scale=0.3333333333333333, maximum-scale=0.3333333333333333, minimum-scale=0.3333333333333333, user-scalable=no"
    />

    <title>tools</title>
    <style>
      #main {
        height: 100px;
        width: 100px;
        &:hover {
          background-color: pink;
          cursor: pointer;
        }
      }
    </style>
  </head>
  <body>
    <div id="main">hello 1</div>
    <!-- <script>
      const script = document.createElement('script');

      script.src = './dist/umd/index.js'; // 设置脚本的 URL
      // script.async = true; // 设置异步加载
      // script.defer = true; // 设置延迟加载

      document.head.appendChild(script);
    </script> -->
    <script
      crossorigin="anonymous"
      src="https://static.huolala.cn/vswitch/335961/dist/umd/index.js#VCONSOLE:UUDDLRLR"
    ></script>
    <script
      src="https://static.huolala.cn/watch-dog-sdk/542444/jssdk.min.js"
      crossorigin="anonymous"
      defer
    ></script>
    <script>
      const d = document.createElement('div');
      d.style.width = '80vw';
      d.style.height = '300px';
      var h = document.createElement('span');
      h.innerText = 'hello';
      d.append(h);
      h.onclick = () => {
        console.log($tool);
        $tool.closeModal();
      };
      const btn = document.createElement('button');
      btn.innerText = 'submit';
      d.append(btn);

      window.$tool.onModalOpen = () => {
        console.log('open');
        h.innerHTML = 'hello';
      };

      window.$tool.onModalClose = () => {
        console.log('close');
      };

      const se = document.createElement('div');
      se.innerText = 'world';
      btn.onclick = () => {
        console.log('1111');
        h.innerText = 'hhhh';
      };
      // init vConsole
      var vConsole = new VConsole();
      new $tool.ToolsContainer({
        name: 'VConsole',
      });
      new $tool.ToolsContainer({
        name: '啊的等待',
        desc: '用户操作全程录制，全面还原问题发生过程。',
        logo: './src/assets/svelte.svg',
        dom: d,
        appear: 'modal',
        title: '123',
        headerStyle: 'background: red',
      });
      // new $tool.ToolsContainer({
      //   name: 'ttt',
      //   logo: './src/assets/img/off.svg',
      //   dom: se,
      //   appear: 'modal',
      //   title: '555',
      //   headerStyle: 'background: yellow',
      // });
      //
    </script>
    <!-- <script src="./dist/umd/index.js"></script> -->

    <!-- <script>
      const script = document.createElement('script');

      script.src = './dist/umd/index.js'; // 设置脚本的 URL
      // script.async = true; // 设置异步加载
      // script.defer = true; // 设置延迟加载

      document.head.appendChild(script);
    </script> -->

    <!-- <script src="https://create-qamp-issue-v.huolala.work/feedback/index.min.js"></script> -->
  </body>
</html>
