{"name": "@hll/tools-container", "version": "1.2.27", "type": "module", "main": "dist/cjs/index.js", "module": "dist/es/index.js", "umd": "dist/umd/index.js", "files": ["dist"], "scripts": {"dev": "vite", "build": "vite build", "build:watch": "vite build --watch", "preview": "vite preview", "check": "svelte-check --tsconfig ./tsconfig.json", "bumpp": "bumpp"}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.24.7", "@babel/preset-env": "^7.24.7", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^21.0.1", "@rollup/plugin-image": "^2.1.1", "@rollup/plugin-node-resolve": "^13.0.6", "@sveltejs/vite-plugin-svelte": "^3.1.1", "@tsconfig/svelte": "^5.0.4", "@types/node": "^20.14.2", "autoprefixer": "^10.3.7", "bumpp": "^9.7.1", "less": "^4.2.0", "path": "^0.12.7", "rollup-plugin-delete": "^2.0.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.1", "rollup-plugin-svelte": "^7.2.2", "rollup-plugin-typescript2": "^0.36.0", "svelte": "^4.2.18", "svelte-check": "^3.6.7", "svelte-preprocess": "^5.1.4", "svelte-preprocess-less": "^0.4.0", "tslib": "^2.6.2", "typescript": "^5.2.2", "vite": "^5.2.12", "vite-plugin-css-injected-by-js": "^3.5.1", "vite-plugin-style-inject": "^0.0.1"}, "dependencies": {"@hll/coupe-sdk": "^3.2.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}