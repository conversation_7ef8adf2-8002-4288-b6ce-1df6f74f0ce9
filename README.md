# Tools-container

这是一个对页面调试工具入口进行依赖收集的容器，整合为统一的展现方式。
如果有多个配置，默认都在 van 图标之下
![multiTools](https://static.huolala.cn/image/3b44cc8a0c27ce226c81cc9286d4780c14a9d443.png)

如果项目只开启了一个配置，则只显示该图标。
![singleMode](https://static.huolala.cn/image/4933d47da3a2f740b2bc2fa75fc103749a95d907.png)

# 说明

建议直接使用cdn，更新 feature 第一时间使用。

## cdn 使用

```html
<script src="https://tools-container-v.huolala.cn/dist/umd/index.js"></script>
<script src="https://create-qamp-issue-v.huolala.work/index.min.js"></script>
<script>
  (function() {
    var $issue = new CreateQampIssue();

    new $tool.ToolsContainer({
      name: 'create-qamp-issue',
      logo: $issue.logo,
      dom: $issue.node,
      appear: 'modal',
      title: '新建 issue'
    });
  })()
</script>
```

## npm 使用（不推荐）
### 安装

```sh
yarn add @hll/tools-container
```

### 使用

```ts
new ToolsContainer({
  /**
   * 必填
   * 工具名称
   **/
  name: string,
  /**
   * 必填
   * 菜单显示的 logo
   * url | base64
   **/
  logo: string
  /**
   * 必填
   * 展示的dom
   **/
  dom: HTMLElement,
  /**
   * 菜单背景色
   **/
  backgroundColor?: string,
  /**
   * 可选
   * 展现方式 modal | drawer | () => void
   * 如果 modal 或者 drawer 方式不满足需求，可以传入 fn
   * 不写默认为 drawer 展开
   **/
  appear?: 'modal' | 'drawer' | () => void,
  /**
   * 展示为 modal 时的标题
   **/
  title?:string
  /**
   * 展示为 modal 时的标题的样式
   **/
  titleStyle?:string
});
```
