import { isMove } from '../store';
import { getObjectKeys } from './index';

function getPosition(evt: TouchEvent | MouseEvent) {
  if (evt instanceof TouchEvent) {
    return evt.touches[0] || evt.changedTouches[0];
  }
  return evt;
}

let hasMove = false;

const DRAG_CLASS = 'van_debug-drag';
const END_DRAG_CLASS = 'van_debug-end-drag';
export const TOOLS_LEFT_CLASS = 'van_debug-position-left';

const topWindow = window;
/* eslint-disable no-param-reassign */
export function moveable(el: HTMLElement) {
  let rect = el.getBoundingClientRect();
  let screenWidth: number;
  let screenHeight: number;
  let critical: { xAxis: number; yAxis: number };
  const touch = { x: 0, y: 0 };
  function start(evt: TouchEvent | MouseEvent) {
    // 因为会拖动调试面板，这些每次都需要在点击时重新计算
    screenWidth = topWindow.innerWidth;
    screenHeight = topWindow.innerHeight;
    critical = {
      xAxis: screenWidth - rect.width,
      yAxis: screenHeight - rect.height,
    };
    isMove.set(false);
    rect = el.getBoundingClientRect();
    const { clientX, clientY } = getPosition(evt);
    touch.x = clientX;
    touch.y = clientY;
  }
  function move(evt: TouchEvent | MouseEvent) {
    hasMove = true;
    evt.preventDefault();
    evt.stopPropagation();
    // 移除在左边的样式，增加拖拽样式
    const { clientX, clientY } = getPosition(evt);
    const diffX = clientX - touch.x;
    const diffY = clientY - touch.y;
    let resultX = rect.x + diffX;
    let resultY = rect.y + diffY;
    // 没移动竟然都会触发move，这里 2px 缓冲拦截一下。
    if (Math.abs(diffX) < 2 && Math.abs(diffY) < 2) {
      return;
    }
    // 5px 以内都算click。
    isMove.set(!(Math.abs(diffY) < 5));

    // 拖拽时 变化
    el.classList.remove(TOOLS_LEFT_CLASS);
    el.classList.add(DRAG_CLASS);

    if (resultX < 0) {
      resultX = 0;
    } else if (resultX > critical.xAxis) {
      resultX = critical.xAxis;
    }
    if (resultY < 0) {
      resultY = 0;
    } else if (resultY > critical.yAxis) {
      resultY = critical.yAxis;
    }
    // （rect.width / 2）  校正在鼠标中心.
    el.style.right = `${
      ((critical.xAxis - resultX + rect.width / 2 - 20) / screenWidth) * 100
    }%`;
    el.style.top = `${(resultY / screenHeight) * 100}%`;
  }
  function end(evt: TouchEvent | MouseEvent) {
    if (!hasMove) return;
    hasMove = false;
    isMove.set(false);
    const { clientX} = getPosition(evt);
    const diffX = clientX - touch.x;
    if (Math.abs(diffX) <= screenWidth / 2) {
      document.documentElement.style.setProperty(
        '--van-debug-run-animate',
        `${Math.abs(diffX) / 250}`
      );
    } else {
      const v = Math.abs((screenWidth - Math.abs(diffX)) / 250);
      document.documentElement.style.setProperty(
        '--van-debug-run-animate',
        `${v}`
      );
    }
    el.classList.remove(DRAG_CLASS);
    el.classList.add(END_DRAG_CLASS);
    if (parseInt(el.style.right, 10) < 50) {
      el.style.right = '0px';
      el.classList.remove(TOOLS_LEFT_CLASS);
    } else {
      el.style.right = `auto`;
      el.classList.add(TOOLS_LEFT_CLASS);
    }
    const top = el.style.top;
    const right = el.style.right;
    localStorage.setItem('hll-tools-style', JSON.stringify({ right, top }));
    touch.y = 0;
    touch.x = 0;
    setTimeout(() => {
      el.classList.remove(END_DRAG_CLASS);
    }, 550);
  }
  const mobileEvents = {
    touchstart: start,
    touchmove: move,
    touchend: end,
  };
  getObjectKeys(mobileEvents).forEach((name) => {
    el.addEventListener(name, mobileEvents[name], false);
  });

  el.addEventListener('mousedown', (ed) => {
    start(ed);
    document.addEventListener('mousemove', move);
    document.addEventListener('mouseup', (eu) => {
      document.removeEventListener('mousemove', move);
      end(eu);
    });
  });
}
