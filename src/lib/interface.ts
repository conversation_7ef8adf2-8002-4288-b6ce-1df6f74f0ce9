interface ExtraProps {
  // 内部用的，是否页面多次引入cdn
  multiImport: boolean
}

export interface Options extends ExtraProps{
  name: string;
  desc?: string;
  logo: string;
  dom?: HTMLElement;
  backgroundColor?: string;
  appear?: 'drawer' | 'modal' | (() => void);
  // modal 的标题
  title?: string;
  titleStyle?: string;
  headerStyle?: string;
  // --
}

export type ConfigMap = Map<string, Options>;

export type TWindowTop = Window | Window['top'];
export type TWindow = TWindowTop & {
  configMap?: ConfigMap;
};
