@import '../common.less';

#tools-drawer {
  font-size: 16px;
  .hll-mask {
    width: 100%;
    height: 0%;
    position: absolute;
    top: 0%;
    left: 0;
    opacity: 0;
    background-color: #00000073;
    transition: opacity 0.3s linear, height 0s ease 0.3s;
    z-index: 10001;
  }
  #drawer-wrapper {
    z-index: 10001;
    width: 0px;
    height: 100%;
    position: fixed;
    right: 0px;
    background-color: #fff;
    border-left: 1px solid #ccc;
    top: 0px;
    transition: all 0.3s ease;
    &.actived {
      width: (400em / @font);
      max-width: 100%;
    }
    .van_content {
      display: flex;
      flex-direction: column;
      color: rgba(0, 0, 0, 0.85);
      line-height: normal;
      .van_header {
        .van_drawer-close {
          color: #ccc;
          cursor: pointer;
          background-color: #fff;
          padding: (5em / @font);
          border-radius: (5em / @font);
          border-bottom: 1px solid #f0f0f0;
          padding-left: (5em / @font);
          display: inline-block;
          img {
            width: (12em / @font);
          }
        }
      }
      .van_body {
        padding: (10em / @font);
        overflow: auto;
        height: 90vh;
      }
    }
  }
}
