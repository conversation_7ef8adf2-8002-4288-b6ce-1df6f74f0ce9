<script lang="ts">
  import closeImg from '@/assets/img/close.svg';
  import { onMount, tick } from 'svelte';
  import { topDocument } from '../utils';
  import { get } from 'svelte/store';
  import { projectDPR } from '../store';

  export let dom: HTMLElement | undefined;

  let show = false;
  const handleClose = () => {
    show = false;
  };

  export const open = () => {
    show = true;
  };

  onMount(() => {
    const targetElement = topDocument.querySelector('.van_body')!;
    if (dom) {
      targetElement.appendChild(dom);
    }
  });

  const dpr = get(projectDPR)
</script>

<div id="tools-drawer" style="font-size: {dpr * 16}px">
  <div class="hll-mask"></div>
  <div id="drawer-wrapper" class:actived={show}>
    <div class="van_content">
      <div class="van_header">
        <div class="van_drawer-close" on:click={handleClose}>
          <img src={closeImg} alt="close" />
        </div>
      </div>
      <div class="van_body"></div>
    </div>
  </div>
</div>

<style lang="less">
  @import './drawer.less';
</style>
