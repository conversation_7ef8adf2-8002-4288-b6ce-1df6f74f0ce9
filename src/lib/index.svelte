<script lang="ts">
  import { moveable } from './utils/moveable';
  import Menu from './Menu/Menu.svelte';
  import { onMount } from 'svelte';
  import { type Options, type TWindow } from './interface';
  import { topDocument } from './utils';
  import vConsoleImg from '@/assets/img/vConsole.svg';
  import pageSpyImg from '@/assets/img/PageSpy2.svg';

  const VConsole = 'VConsole';
  const VConsoleDesc = '一个轻量级可扩展的移动端网页调试工具';

  const PageSpy = 'PageSpy';

  const tWindow: TWindow = window;
  export let options: Options;

  if (!tWindow.configMap) {
    tWindow.configMap = new Map();
  }
  if (options.name === VConsole) {
    options.logo = vConsoleImg;
    options.desc = VConsoleDesc;
  }
  if (options.name === PageSpy) {
    options.logo = pageSpyImg;
  }
  tWindow.configMap.set(options.name, options);

  onMount(() => {
    const root = topDocument.getElementById('hll-tools');
    moveable(root!);
  });
</script>

<Menu
  configMap={tWindow.configMap || new Map()}
  multiImport={options.multiImport}
></Menu>
