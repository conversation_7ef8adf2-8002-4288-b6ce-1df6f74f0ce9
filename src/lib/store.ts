import { get, writable } from 'svelte/store';
// import { CoupeSDK } from '@hll/coupe-sdk';

export const isMove = writable(false);
export const projectDPR = writable(1);

export const coupeConfig = writable<Record<string, any>>({});

// 实例化 sdk
// const sdk = new CoupeSDK({
//   coupeId: 'eae4ca1a55a840f89129a2d153dc8244',
//   env: 'prd',
// });

// export async function fetchCoupeConfig() {
//   if (Object.keys(get(coupeConfig)).length) {
//     return;
//   }
//   const res = await sdk.fetch();
//   coupeConfig.set(res || {});
// }
