<script lang="ts">
  const handleClose = () => {
    (window as any).$tool.closeModal();
  };
  const handleOk = () => {
    sessionStorage.setItem('hll-tools-close', '1');
    (document.querySelector('#hll-tools') as HTMLDivElement).style.display =
      'none';
    (window as any).$tool.closeModal();
  };
</script>

<div class="van_debug_close_modal">
  <span class="van_debug_close_modal-content">本次关闭直到下次访问</span>
  <div class="van_debug_close_modal-btnwrap">
    <button
      class="van_debug_close_modal-btnwrap-item van_debug_close_modal-btnwrap-close"
      on:click={handleClose}>取消</button
    >
    <button
      class="van_debug_close_modal-btnwrap-item van_debug_close_modal-btnwrap-sure"
      on:click={handleOk}>确定</button
    >
  </div>
</div>

<style lang="less">
  .van_debug_close_modal {
    font-size: 0.8125em;
    padding: 0.9375em 0.625em;
    width: 100%;
    margin-right: 10em;
    box-sizing: border-box;
    line-height: normal;
    &-content {
      color: rgba(0, 0, 0, 0.75);
      font-size: 0.9375em;
    }
    &-btnwrap {
      margin-top: 0.625em;
      font-size: 0.9375em;
      display: flex;
      justify-content: flex-end;
      &-item {
        font-size: inherit;
        width: 4.375em;
        height: 1.875em;
        background: #fff;
        border-radius: 0.375em;
        margin-left: 0.625em;
        cursor: pointer;
        color: rgba(0, 0, 0, 0.75);
        transition: all 0.2s ease-in;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      &-close{
        border: 1px solid rgba(204, 204, 204, 0.8);
        &:hover {
          border-color: #f16722;
          color: #f16722;
        }
      }
      &-sure {
        background-color: #f16722;
        border: none;
        color: #fff;
        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
</style>
