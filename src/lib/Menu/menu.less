@import '../common.less';

:global(.vc-switch) {
  display: none !important;
}

:global(#__pageSpy .page-spy-logo) {
  display: none !important;
}

:root {
  --van-debug-run-animate: 0;
}

@keyframes run {
  0% {
    transform: translateX(0px);
  }
  20% {
    transform: translateX(calc(5px * var(--van-debug-run-animate)));
  }
  40% {
    transform: translateX(calc(-3px * var(--van-debug-run-animate)));
  }
  60% {
    transform: translateX(calc(2px * var(--van-debug-run-animate)));
  }
  80% {
    transform: translateX(calc(-1px * var(--van-debug-run-animate)));
  }
  100% {
    transform: translateX(0px);
  }
}

#hll-tools {
  transform: translate3d(0, 0, 0);
  position: fixed;
  z-index: 10000;
  right: 0px;
  bottom: 160px;
  height: 70px;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
  line-height: normal;
  display: block;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  #van-menu {
    position: relative;
    .van-single-menu-tooltip {
      transition: all 0.2s ease-in-out;
      opacity: 0;
      visibility: hidden;
      position: absolute;
      right: 4.5em;
      width: fit-content;
      background-color: rgba(0, 0, 0, 0.85);
      color: #fff;
      border-radius: 0.625em;
      padding: 0.625em 0.9375em;
      font-size: 0.875em;
      cursor: default;
      top: 50%;
      transform: translateY(-50%);
      pointer-events: none;
      // 这里是因为 hover 切换 用了 visibility 和 opacity 增加动画，但是拖拽需要隐藏会有延迟，这里用 none 解决
      display: block;
      &-name {
        font-size: 0.875em;
        color: rgba(255, 255, 255, 0.95);
      }
      &-desc:not(:empty) {
        font-size: 0.8125em;
        color: rgba(255, 255, 255, 0.85);
        width: 12.5em;
        margin-top: 0.125em;
      }
    }
  }
  :global(&.van_debug-drag) {
    cursor: default;
    .inner-van {
      pointer-events: none;
      border-radius: 3.125em;
      width: 2.5em;
      transition: border-radius 150ms linear;
    }
    .van_debug-menu {
      visibility: hidden;
    }
    .van_debug-close {
      visibility: hidden;
    }
    .van-single-menu-tooltip {
      visibility: hidden !important;
      display: none !important;
    }
  }
  :global(&.van_debug-end-drag) {
    transition: right 0.2s linear;
    animation: run 0.3s ease-in forwards 0.2s;
    .van_debug-menu {
      visibility: hidden;
    }
    .van_debug-close {
      visibility: hidden;
    }
    .van-single-menu-tooltip {
      visibility: hidden !important;
      display: none !important;
    }
  }
  &:hover {
    .inner-van {
      opacity: 0.85;
      transform: translateX(0px);
      &::before {
        transform: translateX(125%) skew(-45deg);
        transition: transform 1s ease;
      }
    }
    .van_debug-menu {
      opacity: 0.85;
      transform: translateX(1.375em);
    }
    .van_debug-close {
      transform: translateX(3em);
    }
  }
  .van_debug-logo {
    user-select: none;
  }
  .van_debug-animate {
    transform: translate3d(0, 0, 0);
    transition: transform ease-out 250ms, -webkit-transform ease-out 250ms,
      opacity ease 300ms;
  }
  :global(&.van_debug-position-left) {
    .van_debug-close {
      transform: translateX(-4.375em);
    }
    .inner-van {
      border-top-left-radius: 0px;
      border-bottom-left-radius: 0px;
      border-top-right-radius: 3.125em;
      border-bottom-right-radius: 3.125em;
      transform: translate(-1.5625em);
      img {
        margin-left: 1.3125em;
      }
    }
    .van_debug-menu {
      transform: translateX(-3.125em);
      .icon-container-tooltip {
        left: 3.125em;
      }
    }
    .van-single-menu-tooltip {
      left: 4.375em;
    }
    &:hover {
      .inner-van {
        opacity: 0.85;
        transform: translateX(-0.625em);
      }
      .van_debug-close {
        transform: translateX(0.3125em);
      }
      .van_debug-menu {
        opacity: 0.85;
        transform: translateX(0.4375em);
      }
    }
  }
  .van_debug-close {
    margin-bottom: 0.25em;
    transform: translate(4.375em);
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
    user-select: none;
    img {
      width: 0.875em;
    }
  }
  .inner-van {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    width: 100%;
    height: 100%;
    margin-left: 0.625em;
    // font-size: 0.875em;
    width: 3.5em;
    height: 2.25em;
    line-height: 5em;
    border-top-left-radius: 3.125em;
    border-bottom-left-radius: 3.125em;
    background: #fff;
    box-shadow: 0px 3px 10px 3px rgb(0 0 0 / 20%);
    opacity: 0.65;
    transform: translate(1.25em);
    -webkit-tap-highlight-color: transparent;
    overflow: hidden;
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 99;
      background-image: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0),
        #fff,
        rgba(255, 255, 255, 0)
      );
      transform: translateX(-125%) skew(-45deg);
    }
    img {
      width: 1.25em;
      height: 1.25em;
      margin-left: 0.625em;
    }
  }
  .van_debug-menu {
    margin-top: 0.625em;
    margin-right: 1em;
    border-radius: 1.125em;
    box-shadow: 0px 2px 10px 0px #00000014;
    border: 1px solid #f6f6f6;
    background: #fcfcfc;
    cursor: pointer;
    transform: translateX(4.375em);
    width: 2.25em;
    -webkit-tap-highlight-color: transparent;
    .icon-container {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 2.25em;
      height: 2.25em;
      &-tooltip {
        display: none;
        position: absolute;
        right: 3.125em;
        width: fit-content;
        background-color: rgba(0, 0, 0, 0.85);
        color: #fff;
        border-radius: 0.625em;
        padding: 0.625em 0.9375em;
        font-size: 0.875em;
        cursor: default;
        &-name {
          font-size: 0.875em;
          color: rgba(255, 255, 255, 0.95);
        }
        &-desc:not(:empty) {
          font-size: 0.8125em;
          color: rgba(255, 255, 255, 0.85);
          width: 12.5em;
          margin-top: 0.125em;
        }
      }
      span {
        width: 2.25em;
        height: 2.25em;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        img {
          width: 1.125em;
          height: 1.125em;
        }
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@media (any-hover: hover) {
  #hll-tools {
    .van_debug-menu {
      .icon-container {
        &:hover {
          .icon-container-tooltip {
            display: block;
          }
        }
      }
    }
    &:hover {
      #van-menu {
        .van-single-menu-tooltip {
          display: block;
          opacity: 1;
          visibility: visible;
        }
      }
    }
  }
}
