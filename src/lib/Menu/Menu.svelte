<script lang="ts">
  import vanImg from '@/assets/img/van.svg';
  import circleCloseImg from '@/assets/img/circleClose.svg';
  import type { ConfigMap } from '../interface';
  import Drawer from '../Drawer/Drawer.svelte';
  import Portal from '../Portal/Portal.svelte';
  import Modal from '../Modal/Modal.svelte';
  import { get } from 'svelte/store';
  import { isMove, projectDPR } from '../store';
  import { topDocument } from '../utils';
  import { TOOLS_LEFT_CLASS } from '../utils/moveable';
  import CloseContent from './CloseContent.svelte';

  export let configMap: ConfigMap;
  export let multiImport = false;

  let drawer: Drawer;
  let modal: Modal;
  let closeModal: Modal;
  let title: string | undefined;
  let titleStyle: string | undefined;
  let headerStyle: string | undefined;
  let clickType: '' | 'drawer' | 'modal' = '';
  let showSubMenu = multiImport || configMap.size !== 1;
  let showDebugTool = sessionStorage.getItem('hll-tools-close') !== '1';
  let dom: HTMLElement | undefined;
  

  const dpr = get(projectDPR);

  const handleClickMenu = (e: MouseEvent) => {
    e.stopPropagation()
    if (get(isMove)) {
      return;
    }
    const target = e.target as HTMLElement;
    const toolName = target.dataset.toolName || '';
    if (toolName === 'VConsole') {
      (
        topDocument.getElementsByClassName('vc-switch')[0] as HTMLDivElement
      ).click();
    } else if (toolName === 'PageSpy') {
      const logo = topDocument.querySelector(
        '#__pageSpy .page-spy-logo'
      ) as HTMLDivElement;
      if (!logo) return;
      logo.click();
    } else {
      const op = configMap?.get(toolName);
      if (op?.appear === 'drawer' || !op?.appear) {
        dom = op?.dom;
        clickType = 'drawer';
        setTimeout(() => {
          drawer?.open();
        });
      } else if (op?.appear === 'modal') {
        dom = op?.dom;
        title = op?.title;
        titleStyle = op?.titleStyle;
        headerStyle = op?.headerStyle;
        clickType = 'modal';
        setTimeout(() => {
          modal?.open();
        });
      } else if (typeof op?.appear === 'function') {
        op.appear();
      }
    }
  };

  const handleClickMainMenu = (e: MouseEvent) => {
    if (showSubMenu) {
      return;
    }
    handleClickMenu(e);
  };

  const getMainLogo = (configMap: ConfigMap) => {
    if (!showSubMenu) {
      const [_, { logo, name, desc }] = [...configMap.entries()][0];
      return {
        logo: logo || vanImg,
        name: name || 'van',
        desc: desc || '',
      };
    }
    return {
      logo: vanImg,
      name: 'van',
    };
  };
  $: mainLogo = getMainLogo(configMap);
  
  const toolsStyle = JSON.parse(
    localStorage.getItem('hll-tools-style') || '{}'
  );
  const top = toolsStyle?.top;
  const right = toolsStyle?.right;

  // 生成关闭弹窗里面的dom
  let closeModalContent: HTMLDivElement;

  const handleClose = () => {
    const tempDom = document.createElement('div');
    new CloseContent({
      target: tempDom,
    });

    closeModalContent = tempDom;
    setTimeout(() => {
      closeModal?.open();
    });
  };

  const stopPropagation = (e:MouseEvent) => {
    e.stopPropagation()
  }
</script>

<div
  id="hll-tools"
  class={right === 'auto' ? TOOLS_LEFT_CLASS : ''}
  style="top: {top ? top : 'auto'};right: {right
    ? right
    : '0px'};display: {showDebugTool ? 'block' : 'none'}; font-size: {dpr * 16}px"
>
  <div class="van_debug-close van_debug-animate">
    <img src={circleCloseImg} alt="close" on:click={handleClose} />
  </div>
  {#if closeModalContent}
    <Portal>
      <Modal
        bind:this={closeModal}
        dom={closeModalContent}
        title="关闭工具栏"
      />
    </Portal>
  {/if}
  <div id="van-menu">
    <div
      class="inner-van van_debug-animate"
      data-tool-name={mainLogo.name}
      on:click={handleClickMainMenu}
      style="cursor: {showSubMenu ? 'default' : 'pointer'}"
    >
      <img
        src={mainLogo.logo}
        data-tool-name={mainLogo.name}
        draggable="false"
        alt="brand"
        class="van_debug-logo"
      />
    </div>
    {#if !showSubMenu}
      <div class="van-single-menu-tooltip">
        <div class="van-single-menu-tooltip-name">{mainLogo.name || ''}</div>
        <div class="van-single-menu-tooltip-desc">{mainLogo.desc || ''}</div>
      </div>
    {/if}
  </div>
  {#if showSubMenu}
    <div class="van_debug-menu van_debug-animate" on:click={handleClickMenu} on:mousedown={stopPropagation}>
      {#each Array.from(configMap.entries()) as [key, value]}
        <div class="icon-container" data-tool-name={value.name}>
          <span data-tool-name={value.name}
            ><img
              data-tool-name={value.name}
              src={value.logo}
              alt="logo"
              class="van_debug-logo"
              draggable="false"
            /></span
          >
          <div class="icon-container-tooltip">
            <div class="icon-container-tooltip-name">{value.name || ''}</div>
            <div class="icon-container-tooltip-desc">{value.desc || ''}</div>
          </div>
        </div>
      {/each}
    </div>
  {/if}
  {#if dom}
    <Portal>
      {#if clickType === 'drawer'}
        <Drawer bind:this={drawer} {dom} />
      {:else if clickType === 'modal'}
        <Modal bind:this={modal} {dom} {title} {titleStyle} {headerStyle} />
      {/if}
    </Portal>
  {/if}
</div>

<style lang="less">
  @import './menu.less';
</style>
