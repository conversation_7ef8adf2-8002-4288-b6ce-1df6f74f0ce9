import App from './index.svelte';
import { projectDPR } from './store';
const createDom = () => {
  let root = document.getElementById('van-tools') as HTMLDivElement;
  if (root) {
    root.innerHTML = '';
  } else {
    root = document.createElement('div');
    root.setAttribute('id', 'van-tools');
    document.documentElement.insertAdjacentElement('beforeend', root);
  }
  return root;
};

const handleDeviceDPR = () => {
  const dpr = window.devicePixelRatio || 1;
  const viewportEl = document.querySelector('[name="viewport"]');

  if (viewportEl) {
    const viewportContent = viewportEl.getAttribute('content') || '';
    const initialScale = viewportContent.match(/initial-scale=\d+(\.\d+)?/);
    const scale = initialScale ? parseFloat(initialScale[0].split('=')[1]) : 1;
    if (scale < 1) {
      projectDPR.set(dpr);
    }
  }
};

/**
 * 为什么这样做，因为有些使用方式是通过引入自己的 cdn，动态插入本项目 cdn 到 head 中，图标会闪一下
 * 如果收集依赖，一起初始化，以上说已经在 onload 之后了，收集不到时机太晚。
 */
const URL = 'https://tools-container-v.huolala.cn/dist/umd/index.js';
// const URL = './dist/umd/index.js';
class InitClass {
  constructor(args: any) {
    const scriptTags = document.querySelectorAll(`script[src="${URL}"]`);
    const multiImport = scriptTags.length > 1;
    handleDeviceDPR()
    new App({
      target: createDom(),
      props: {
        options: {
          ...args,
          multiImport,
        },
      },
    });
  }
}

const fe = {
  ToolsContainer: InitClass,
};

export default fe;

export const ToolsContainer = fe.ToolsContainer;
