<script lang="ts">
  import { onMount } from 'svelte';
  import { topDocument } from '../utils';
  let ref: HTMLElement;
  let portal: HTMLElement;

  portal = topDocument.createElement('div');
  portal.className = 'portal';
  topDocument.documentElement.insertAdjacentElement('beforeend', portal);

  onMount(() => {
    portal.appendChild(ref);
  });
</script>

<div bind:this={ref}>
  <slot></slot>
</div>
