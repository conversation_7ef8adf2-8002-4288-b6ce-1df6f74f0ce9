@import '../common.less';

@keyframes modalOpenBounce {
  0% {
    transform: scale(0.2) translateY(-20px);
    opacity: 0;
  }
  60% {
    transform: scale(1.05) translateY(0);
    opacity: 1;
  }
  80% {
    transform: scale(0.97) translateY(0);
    opacity: 1;
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes modalCloseBounce {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  20% {
    transform: scale(1.05);
    opacity: 0.9;
  }
  100% {
    transform: scale(0.9);
    opacity: 0;
  }
}

#modal-wrapper {
  visibility: hidden;
  font-size: 16px;
  &.actived {
    visibility: visible;
  }
  .hll-mask {
    width: 100%;
    height: 100%;
    position: fixed;
    z-index: 10001;
    top: 0;
    left: 0;
    background-color: #00000073;
    visibility: hidden;
    opacity: 0;
    transition: all 0.3s ease;
    &.actived {
      visibility: visible;
      opacity: 1;
    }
  }
  .van_content {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    z-index: 10001;
    background-color: #fff;
    width: fit-content;
    height: fit-content;
    max-height: 25em;
    margin: auto;
    border-radius: 0.3125em;
    visibility: hidden;
    transform: scale(0.2) translateY(-20px);
    transform-origin: center center;
    overflow: hidden;
    color: rgba(0, 0, 0, 0.85);
    line-height: normal;
    opacity: 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    &.actived {
      visibility: visible;
      animation: modalOpenBounce 0.45s forwards;
    }
    &.closing {
      visibility: visible;
      animation: modalCloseBounce 0.35s forwards;
    }
    .van_header {
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      justify-content: space-between;
      padding: 0.5em 0.75em;
      align-items: center;
      &_title {
        font-size: 1em;
        opacity: 0.65;
      }
      .van_modal-close {
        align-self: center;
        font-size: 1em;
        cursor: pointer;
        display: inline-block;
        svg {
          width: 0.75em;
          height: 0.75em;
        }
      }
    }
    .van_body {
      overflow: auto;
    }
  }
}
