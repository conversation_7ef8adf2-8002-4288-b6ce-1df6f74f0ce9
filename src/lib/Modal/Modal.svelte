<script lang="ts">
  import closeImg from '@/assets/img/close.svg?raw';
  import { get } from 'svelte/store';
  import { projectDPR } from '../store';

  export let dom: HTMLElement | undefined;
  export let title: string | undefined = '';
  export let titleStyle: string | undefined = '';
  export let headerStyle: string | undefined = '';

  let show = false;
  let closing = false;

  const handleClose = () => {
    closing = true;
    setTimeout(() => {
      show = false;
      closing = false;
    }, 350);
    typeof (window as any).$tool?.onModalClose === 'function' &&
      (window as any).$tool?.onModalClose();
  };

  (window as any).$tool.closeModal = handleClose;

  let root: HTMLDivElement;
  export const open = () => {
    show = true;
    const targetElement = root.querySelector('.van_body')!;
    if (dom) {
      targetElement.innerHTML = '';
      targetElement.appendChild(dom);
    }
    typeof (window as any).$tool?.onModalOpen === 'function' &&
      (window as any).$tool?.onModalOpen();
  };

  function isHTMLElement(obj: any): obj is HTMLElement {
    return obj instanceof HTMLElement;
  }

  const dpr = get(projectDPR);
</script>

<div
  id="modal-wrapper"
  class:actived={show}
  bind:this={root}
  style="font-size: {dpr * 16}px"
>
  <div class="hll-mask" class:actived={show} on:click={handleClose}></div>
  <div class="van_content" class:actived={show} class:closing={closing}>
    <div class="van_header" style={headerStyle}>
      {#if typeof title === 'string' && title.startsWith('<')}
        {@html title}
      {:else if isHTMLElement(title)}
        {@html title.outerHTML}
      {:else}
        <span class="van_header_title" style={titleStyle}>{title}</span>
      {/if}
      <div class="van_modal-close" on:click={handleClose}>
        {@html closeImg}
      </div>
    </div>
    <div class="van_body"></div>
  </div>
</div>

<style lang="less">
  @import './modal.less';
</style>
