<svg
  viewBox="0 0 240 200"
  xmlns="http://www.w3.org/2000/svg"
  xmlns:xlink="http://www.w3.org/1999/xlink"
  fill="none"
  stroke="#000"
  stroke-width="12"
>
  <defs>
    <g id="shoes">
      <circle cx="70" cy="166" r="16" />
      <circle cx="170" cy="166" r="16" />
    </g>
    <rect
      id="mainRect"
      x="20"
      y="50"
      rx="30"
      ry="30"
      width="200"
      height="116"
    />
    <mask id="mask1">
      <rect width="100%" height="100%" fill="#fff" />
      <use xlink:href="#mainRect" fill="#000" />
    </mask>
    <mask id="mask2">
      <rect width="100%" height="100%" fill="#fff" />
      <use xlink:href="#shoes" fill="#000" />
    </mask>
  </defs>
  <rect
    x="20"
    y="20"
    rx="30"
    ry="30"
    width="150"
    height="90"
    mask="url(#mask1)" 
  />
  <use xlink:href="#mainRect" mask="url(#mask2)" />
  <use xlink:href="#shoes" />
  <path
    transform="translate(100, 86)"
    stroke-linecap="round"
    stroke-linejoin="round"
    d="M 0 0 l -20 20 l 20 20 M 35 0 l -20 40 M 50 0 l 20 20 l -20 20" />
</svg>
