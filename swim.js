(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
  typeof define === 'function' && define.amd ? define(factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.$swim = factory());
})(this, (function () { 'use strict';

  const swimEnvID = 'swim-env';
  const getSwimEnv = window.localStorage.getItem(swimEnvID);
  const URL = getSwimEnv === 'dev' ? 'https://van-api-stg.huolala.work' : 'https://van-api.huolala.work';
  const projectName =
  // @ts-ignore
  window.$swimConfig && window.$swimConfig.project || '';
  function getProjectInfo() {
    return fetch("".concat(URL, "/project/task/open?name=").concat(projectName, "&page=1&size=50")).then(r => r.json()).catch(err => {
      throw Error("\u83B7\u53D6\u9879\u76EE\u7248\u672C\u4FE1\u606F\u5931\u8D25\uFF1A ".concat(err.message));
    });
  }

  var img$3 = "data:image/svg+xml,%3csvg t='1636010335572' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='21156' width='200' height='200'%3e%3cpath d='M452 78.9H144.6c-26.5 0-48 21.5-48 48v322.7c0 26.5 21.5 48 48 48H452c26.5 0 48-21.5 48-48V126.9c0-26.5-21.6-48-48-48z m-24 346.7H168.6V150.9H428v274.7zM878.7 525.6H571.3c-26.5 0-48 21.5-48 48v322.7c0 26.5 21.5 48 48 48h307.4c26.5 0 48-21.5 48-48V573.6c0-26.5-21.5-48-48-48z m-24 346.7H595.3V597.6h259.4v274.7zM417.7 872.3c-0.6 0-63.4-0.4-126.2-32.4-65.6-33.4-105.2-88.3-118.2-163.6h23c7.6 0 12.4-8.2 8.8-14.8l-61-110.5c-3.8-6.9-13.7-6.9-17.5 0l-61 110.5c-3.7 6.7 1.1 14.8 8.8 14.8h26.1C118.2 805.2 194 871 258.8 904c78.1 39.8 155.7 40.3 158.9 40.3 19.9 0 36-16.1 36-36s-16.1-36-36-36zM949.6 347.1h-26.1c-17.8-129-93.5-194.7-158.4-227.7C687 79.6 609.4 79.1 606.2 79.1c-19.9 0-36 16.1-36 36s16.1 36 36 36c0.6 0 63.4 0.4 126.2 32.4 65.6 33.4 105.2 88.3 118.2 163.6h-23c-7.6 0-12.4 8.2-8.8 14.8l61 110.5c3.8 6.9 13.7 6.9 17.5 0l61-110.5c3.8-6.6-1-14.8-8.7-14.8z' p-id='21157'%3e%3c/path%3e%3c/svg%3e";

  function getCookie(name) {
    const obj = {};
    document.cookie.split('; ').forEach(r => {
      const kv = r.split('=');
      obj[kv[0]] = kv[1];
    });
    if (name) return obj[name];
    return obj;
  }
  function getEnv() {
    return document.documentElement.dataset.vanEnv || 'stg';
  }
  function insertScript(src) {
    let done = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : () => {};
    let error = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : () => {};
    const script = document.createElement('script');
    script.src = src;
    script.crossOrigin = 'anonymous';
    script.addEventListener('load', done);
    script.addEventListener('error', error);
    document.head.insertAdjacentElement('afterbegin', script);
  }

  var img$2 = "data:image/svg+xml,%3csvg t='1636104218898' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='3082' width='200' height='200'%3e%3cpath d='M512.768 341.333333C406.613333 341.333333 319.146667 426.837333 319.146667 534.101333S406.613333 725.333333 512.853333 725.333333c106.197333 0 193.664-85.504 193.664-192.768S619.008 341.333333 512.768 341.333333z m0 290.730667a99.413333 99.413333 0 0 1-99.925333-99.498667c0-55.978667 43.690667-99.498667 99.925333-99.498666s99.968 43.52 99.968 99.498666a99.413333 99.413333 0 0 1-99.968 99.498667z' p-id='3083'%3e%3c/path%3e%3cpath d='M365.952 485.930667H89.514667c-26.538667 0-46.848 20.181333-46.848 46.634666s20.309333 46.634667 46.848 46.634667h276.48c26.538667 0 46.848-20.224 46.848-46.634667 0-26.453333-20.309333-46.634667-46.890667-46.634666zM934.485333 485.930667h-276.48c-26.538667 0-46.848 20.181333-46.848 46.634666s20.309333 46.634667 46.890667 46.634667h276.437333c26.538667 0 46.848-20.224 46.848-46.634667 0-26.453333-20.309333-46.634667-46.848-46.634666z' p-id='3084'%3e%3c/path%3e%3c/svg%3e";

  var img$1 = "data:image/svg+xml,%3csvg t='1662715105264' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='2684' width='200' height='200'%3e%3cpath d='M512 960c-102.464 0-196.906667-34.410667-272.384-92.288l-2.730667-2.133333-0.469333-0.341334a450.133333 450.133333 0 0 1-9.749333-7.829333l10.218666 8.192a450.154667 450.154667 0 0 1-51.2-46.634667l-2.517333-2.709333A446.4 446.4 0 0 1 64 512C64 264.576 264.576 64 512 64s448 200.576 448 448c0 117.461333-45.226667 224.362667-119.168 304.256l-2.517333 2.709333-0.426667 0.448c-2.602667 2.773333-5.248 5.504-7.936 8.192l8.362667-8.64a450.816 450.816 0 0 1-51.2 46.634667l-2.730667 2.133333A446.037333 446.037333 0 0 1 512 960z m6.72-682.666667h-12.949333l-4.842667 0.064a139.050667 139.050667 0 0 0-134.613333 133.994667l-0.085334 4.992v95.872l0.085334 5.909333a145.770667 145.770667 0 0 0 38.976 93.994667l4.586666 4.693333 43.904 43.178667 3.456 3.84c9.088 10.88 14.506667 24.384 15.488 38.485333l0.170667 4.736-0.042667 7.552-0.256 4.693334a65.024 65.024 0 0 1-33.194666 50.645333l-3.818667 1.962667-132.394667 62.378666A382.208 382.208 0 0 0 512 896c77.013333 0 148.693333-22.656 208.810667-61.674667l-132.394667-62.378666a65.024 65.024 0 0 1-37.162667-54.656l-0.149333-4.181334v-6.016c0-16.64 6.122667-32.597333 17.109333-44.949333l3.114667-3.264 42.794667-42.026667a145.770667 145.770667 0 0 0 43.52-97.493333l0.128-6.506667v-96.469333a139.050667 139.050667 0 0 0-134.058667-138.965333L518.72 277.333333zM512 128C299.925333 128 128 299.925333 128 512c0 109.376 45.738667 208.085333 119.125333 278.016l161.557334-76.117333c0.064 0.021333 0.106667 0.128 0.106666 0.341333l-0.021333 0.405333 0.128-1.536v-6.016c0-0.64-0.170667-1.237333-0.021333-1.28l0.256 0.213334-1.301334-1.493334-42.794666-42.005333a209.984 209.984 0 0 1-62.314667-135.552l-0.384-7.04-0.106667-7.082667v-96.469333c0-107.861333 84.224-196.437333 191.466667-202.709333l5.76-0.256 5.824-0.085334h13.44a203.050667 203.050667 0 0 1 202.986667 197.269334l0.064 5.781333v96.469333c0 53.952-20.778667 105.749333-57.877334 144.682667l-4.906666 4.992-42.816 42.026667a3.562667 3.562667 0 0 0-0.938667 1.6l-0.128 0.938666v6.016c0 0.277333 0.106667 0.512 0.277333 0.704l0.32 0.213334 161.194667 75.968A382.912 382.912 0 0 0 896 512c0-212.074667-171.925333-384-384-384z' fill='%23333333' p-id='2685'%3e%3c/path%3e%3c/svg%3e";

  var img = "data:image/svg+xml,%3csvg t='1636108028789' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='4911' width='200' height='200'%3e%3cpath d='M521.1 380.6c21.4 15.8 53.9 6.2 72.5-21.3s16.3-62.7-5.1-78.5c-21.4-15.7-53.9-6.2-72.5 21.4-18.6 27.4-16.3 62.5 5.1 78.4zM553.7 682s-56.4 35.3-42.4-9.3c0 0 4.5-19.5 19.9-60.4 12.3-32.7 32.4-78.9 57-140.4 0 0 12.9-31-41.1-25.1-23.3 2.5-51.9 13.1-75.5 25.1-30.9 15.7-54.7 34.1-62.3 40.9l18.5 11.2s104.6-78.1 78.2-13c-26.4 65-68.9 160.9-67.6 199.9 1.3 39 72.9 15.9 72.9 15.9s19.2-8.3 41.1-20.5c28.9-16.1 62.3-38.2 62.3-38.2L597.5 656l-43.8 26zM511.9 93.4c-226.2 0-409.6 183.4-409.6 409.6 0 226.2 183.4 409.6 409.6 409.6 226.2 0 409.6-183.4 409.6-409.6 0-226.2-183.4-409.6-409.6-409.6z m254.3 663.9C698.3 825.2 608 862.6 511.9 862.6c-96.1 0-186.4-37.4-254.3-105.3S152.3 599.1 152.3 503s37.4-186.4 105.3-254.3 158.2-105.3 254.3-105.3c96 0 186.3 37.4 254.3 105.3S871.5 407 871.5 503c0 96.1-37.4 186.4-105.3 254.3z' p-id='4912' fill='%232c2c2c'%3e%3c/path%3e%3c/svg%3e";

  function styleInject(css, ref) {
    if ( ref === void 0 ) ref = {};
    var insertAt = ref.insertAt;

    if (!css || typeof document === 'undefined') { return; }

    var head = document.head || document.getElementsByTagName('head')[0];
    var style = document.createElement('style');
    style.type = 'text/css';

    if (insertAt === 'top') {
      if (head.firstChild) {
        head.insertBefore(style, head.firstChild);
      } else {
        head.appendChild(style);
      }
    } else {
      head.appendChild(style);
    }

    if (style.styleSheet) {
      style.styleSheet.cssText = css;
    } else {
      style.appendChild(document.createTextNode(css));
    }
  }

  var css_248z$1 = ".van_content {\n  font-size: 16px;\n  color: black;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';\n  line-height: normal;\n}\n.version-card-list {\n  display: flex;\n  flex-direction: column;\n  gap: 25px;\n  margin: 0px 10px;\n}\n.version-card-list #stable-btn-container {\n  display: flex;\n  gap: 20px;\n  flex-wrap: wrap;\n}\n.version-card-list #stable-btn-container .stable-btn {\n  border: 1px solid #efefef;\n  width: 20vw;\n  text-align: center;\n  border-radius: 8px;\n  padding: 10px;\n  cursor: pointer;\n}\n.version-card-list #stable-btn-container .stable-btn.active {\n  outline: 2px solid #1890ff;\n}\n.version-card-list .version-card {\n  position: relative;\n  padding: 10px 12px;\n  border: 1px solid #efefef;\n  border-radius: 3px;\n  cursor: pointer;\n  transition: 0.3s border-color ease;\n}\n.version-card-list .version-card.actived {\n  outline: 2px solid #1890ff;\n}\n.version-card-list .version-card img {\n  width: 8%;\n  margin-right: 2px;\n  vertical-align: middle;\n}\n.version-card-list .version-card .commit-row {\n  margin: 10px 0px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n}\n";
  styleInject(css_248z$1);

  // 30 (min)
  const COOKIE_EXPIRE_TIME = 30;
  const genVersionCardTpl = (version, commit, nowVersion, person) => "\n    <div class=\"version-card ".concat(version === nowVersion ? 'actived' : '', "\" data-version=\"").concat(version, "\">\n        <div class=\"version-row\">\n            <img src=\"").concat(img, "\" />\n            <span class=\"version\">").concat(version, "</span>\n        </div>\n        <div class=\"commit-row\">\n            <img src=\"").concat(img$2, "\" />\n            <span class=\"commit\">").concat(commit, "</span>\n        </div>\n        <div class=\"person-row\">\n            <img src=\"").concat(img$1, "\" />\n            <span class=\"person\">").concat(person, "</span>\n        </div>\n    </div>\n");
  const setVanCookie = version => {
    const args = ["van_debug_".concat(projectName, "=").concat(version), 'Path=/'];
    const dMatches = document.domain.match(/(^|\.)(huolala|xlcx)\.(work|cn)$/g);
    if (dMatches) args.push("Domain=".concat(dMatches[0]));
    const expirationDate = new Date();
    expirationDate.setTime(expirationDate.getTime() + COOKIE_EXPIRE_TIME * 60 * 1000);
    args.push("Expires=".concat(expirationDate.toUTCString()));
    document.cookie = args.join('; ');
  };
  const genCardsContainer = options => {
    var _document$documentEle;
    const cardContainer = document.createElement('div');
    cardContainer.className = 'version-card-list';
    const list = [];
    const v = parseInt(((_document$documentEle = document.documentElement.dataset) === null || _document$documentEle === void 0 ? void 0 : _document$documentEle.vanTaskId) || '0', 10);
    options.forEach(option => {
      list.push(genVersionCardTpl(option.id, option.commit_message, v, option.username));
    });
    cardContainer.insertAdjacentHTML('beforeend', list.join(''));
    // cookie 中没有回填的 version，先找 van_version_[projectName]_[env], 默认第一个兜底
    if (!v) {
      const vanVersion = getCookie("van_version_".concat(projectName, "_").concat(getEnv()));
      if (vanVersion) {
        const childList = Array.from(cardContainer.children);
        for (let i = 0; i < childList.length; i++) {
          const child = childList[i];
          if (child.dataset.version === vanVersion) {
            child.classList.add('actived');
            break;
          }
        }
      } else {
        var _cardContainer$childr;
        (_cardContainer$childr = cardContainer.children[0]) === null || _cardContainer$childr === void 0 || _cardContainer$childr.classList.add('actived');
      }
    }
    cardContainer.addEventListener('click', e => {
      const event = e || window.event;
      let target = event.target || event.srcElement;
      while (!((_target = target) !== null && _target !== void 0 && _target.classList.contains('version-card'))) {
        var _target, _target2;
        if (target === cardContainer) {
          target = null;
          break;
        }
        target = (_target2 = target) === null || _target2 === void 0 ? void 0 : _target2.parentNode;
      }
      if (target) {
        var _document$getElements, _target3;
        (_document$getElements = document.getElementsByClassName('version-card actived')[0]) === null || _document$getElements === void 0 || _document$getElements.classList.remove('actived');
        (_target3 = target) === null || _target3 === void 0 || _target3.classList.add('actived');
        setVanCookie(target.dataset.version);
        window.location.reload();
      }
    });
    return cardContainer;
  };

  var css_248z = ".swim-line-content {\n  border-radius: 5px;\n  text-align: center;\n  cursor: auto;\n}\n.swim-line-content__info {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  padding: 20px 50px;\n  border-bottom: 1px solid #f0f0f0;\n}\n.swim-line-content__info .container {\n  display: flex;\n  width: 100%;\n}\n.swim-line-content__info .container select {\n  font-size: inherit;\n  padding-right: 3px;\n  width: 100%;\n  outline: none;\n  border: 1px solid #ccc;\n}\n.swim-line-content .btn-wrapper {\n  display: flex;\n  width: 100%;\n  margin-top: 10px;\n}\n.swim-line-content .btn-wrapper .btn {\n  flex: 1;\n  border: none;\n  font-size: inherit;\n  line-height: 1.5;\n}\n.swim-line-content .btn-wrapper .ok {\n  background-color: #fff;\n  color: #0591f5;\n  cursor: pointer;\n}\n";
  styleInject(css_248z);

  // 用于切换 stable 环境的切换
  const SWITCH_TPL = "\n    <div class=\"stable-btn\">stable</div>\n    <div class=\"stable-btn\">stable-1</div>\n    <div class=\"stable-btn\">stable-2</div>\n    <div class=\"stable-btn\">stable-3</div>\n    <div class=\"stable-btn\">stable-4</div>\n";
  const listenBtn = parent => {
    parent.addEventListener('click', e => {
      var _document$getElements;
      const target = e.target;
      const content = target.textContent;
      if (content) {
        setVanCookie(content);
        window.location.reload();
      }
      (_document$getElements = document.getElementsByClassName('stable-btn active')[0]) === null || _document$getElements === void 0 || _document$getElements.classList.remove('active');
      target.classList.add('active');
    });
  };
  const initHighlightBtn = wrapper => {
    var _document$documentEle;
    // stable 切换完成之后， vanSec 还是不会变，以 cookie 为准。
    const envStable = getCookie("van_debug_".concat(projectName)) || ((_document$documentEle = document.documentElement.dataset) === null || _document$documentEle === void 0 ? void 0 : _document$documentEle.vanSec);
    if (envStable) {
      const elements = wrapper.getElementsByClassName('stable-btn');
      for (let i = 0; i < elements.length; i++) {
        if (elements[i].textContent === envStable) {
          elements[i].classList.add('active');
        }
      }
    }
  };
  const generateSwitchButton = parent => {
    const wrapper = document.createElement('div');
    wrapper.id = 'stable-btn-container';
    wrapper.insertAdjacentHTML('afterbegin', SWITCH_TPL);
    parent.insertAdjacentElement('afterbegin', wrapper);
    initHighlightBtn(wrapper);
    listenBtn(wrapper);
  };

  function _classPrivateFieldInitSpec(e, t, a) { _checkPrivateRedeclaration(e, t), t.set(e, a); }
  function _checkPrivateRedeclaration(e, t) { if (t.has(e)) throw new TypeError("Cannot initialize the same private elements twice on an object"); }
  function _classPrivateFieldGet(s, a) { return s.get(_assertClassBrand(s, a)); }
  function _classPrivateFieldSet(s, a, r) { return s.set(_assertClassBrand(s, a), r), r; }
  function _assertClassBrand(e, t, n) { if ("function" == typeof e ? e === t : e.has(t)) return arguments.length < 3 ? t : n; throw new TypeError("Private element is not present on this object"); }
  const ID = 'swim-line';
  var _data = /*#__PURE__*/new WeakMap();
  class SwimLine {
    constructor() {
      _classPrivateFieldInitSpec(this, _data, []);
      const root = document.getElementById(ID);
      if (root) {
        console.error('swim line feature has been inited');
      }
      const {
        vanEnv
      } = document.documentElement.dataset;
      if (vanEnv === 'prod') return;
      window.addEventListener('DOMContentLoaded', () => {
        this.init();
      });
    }
    preInit() {
      return new Promise((resolve, reject) => {
        insertScript('https://tools-container-v.huolala.cn/dist/umd/index.js', resolve, reject);
      });
    }
    init() {
      getProjectInfo().then(async res => {
        _classPrivateFieldSet(_data, this, res.data || []);
        await this.preInit();
        this.render();
      }).catch(e => {
        console.log(e);
      });
    }
    render() {
      const cardsContainerDom = genCardsContainer(_classPrivateFieldGet(_data, this));
      const tips = document.createElement('span');
      tips.textContent = '点击卡片即可根据 cookie 切换至该版本';
      const firstChild = cardsContainerDom.children[0];
      generateSwitchButton(cardsContainerDom);
      cardsContainerDom.insertBefore(tips, firstChild);
      // eslint-disable-next-line no-new
      new window.$tool.ToolsContainer({
        name: 'line',
        logo: img$3,
        dom: cardsContainerDom,
        backgroundColor: '#fff',
        appear: 'drawer'
      });
      console.log('[Version] 初始化成功');
    }
  }
  var index = new SwimLine();

  return index;

}));
//# sourceMappingURL=index.js.map
