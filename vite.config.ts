import { defineConfig } from 'vite';
import { svelte } from '@sveltejs/vite-plugin-svelte';
import { resolve } from 'path';
import del from 'rollup-plugin-delete';
import image from '@rollup/plugin-image';
import { less } from 'svelte-preprocess-less';
// import cssInjectedByJsPlugin from 'vite-plugin-css-injected-by-js';
import VitePluginStyleInject from 'vite-plugin-style-inject';

export default defineConfig({
  plugins: [
    svelte({
      preprocess: {
        style: less(),
      },
    }),
    VitePluginStyleInject(),
    image(),
    del({ targets: ['dist/*'] }),
  ],
  build: {
    sourcemap: true,
    lib: {
      entry: resolve(__dirname, 'src/lib/index.ts'),
      name: '$tool',
      fileName: (format) => `${format}/index.js`,
      formats: ['es', 'cjs', 'umd'],
    },
    target: 'es2015',
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
});
